{"cells": [{"cell_type": "markdown", "id": "78c58ca9", "metadata": {}, "source": ["# SALES DATA ANALYSIS\n"]}, {"cell_type": "markdown", "id": "load_data_title", "metadata": {}, "source": ["## Load and Process Data"]}, {"cell_type": "code", "execution_count": 71, "id": "a29c5b84", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_1728\\1335406778.py:15: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  df['Order Date'] = pd.to_datetime(df['Order Date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 185,950 records, Total Sales: $34,492,036\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Order ID</th>\n", "      <th>Product</th>\n", "      <th>Quantity Ordered</th>\n", "      <th>Price Each</th>\n", "      <th>Order Date</th>\n", "      <th>Purchase Address</th>\n", "      <th>Sales</th>\n", "      <th>Month</th>\n", "      <th>Hour</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>176558</td>\n", "      <td>USB-C Charging Cable</td>\n", "      <td>2</td>\n", "      <td>11.95</td>\n", "      <td>2019-04-19 08:46:00</td>\n", "      <td>917 1st St, Dallas, TX 75001</td>\n", "      <td>23.90</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>Dallas</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>176559</td>\n", "      <td>Bose SoundSport Headphones</td>\n", "      <td>1</td>\n", "      <td>99.99</td>\n", "      <td>2019-04-07 22:30:00</td>\n", "      <td>682 Chestnut St, Boston, MA 02215</td>\n", "      <td>99.99</td>\n", "      <td>4</td>\n", "      <td>22</td>\n", "      <td>Boston</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>176560</td>\n", "      <td>Google Phone</td>\n", "      <td>1</td>\n", "      <td>600.00</td>\n", "      <td>2019-04-12 14:38:00</td>\n", "      <td>669 Spruce St, Los Angeles, CA 90001</td>\n", "      <td>600.00</td>\n", "      <td>4</td>\n", "      <td>14</td>\n", "      <td>Los Angeles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>176560</td>\n", "      <td>Wired Headphones</td>\n", "      <td>1</td>\n", "      <td>11.99</td>\n", "      <td>2019-04-12 14:38:00</td>\n", "      <td>669 Spruce St, Los Angeles, CA 90001</td>\n", "      <td>11.99</td>\n", "      <td>4</td>\n", "      <td>14</td>\n", "      <td>Los Angeles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>176561</td>\n", "      <td>Wired Headphones</td>\n", "      <td>1</td>\n", "      <td>11.99</td>\n", "      <td>2019-04-30 09:27:00</td>\n", "      <td>333 8th St, Los Angeles, CA 90001</td>\n", "      <td>11.99</td>\n", "      <td>4</td>\n", "      <td>9</td>\n", "      <td>Los Angeles</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Order ID                     Product  Quantity Ordered  Price Each  \\\n", "0   176558        USB-C Charging Cable                 2       11.95   \n", "2   176559  Bose SoundSport Headphones                 1       99.99   \n", "3   176560                Google Phone                 1      600.00   \n", "4   176560            Wired Headphones                 1       11.99   \n", "5   176561            Wired Headphones                 1       11.99   \n", "\n", "           Order Date                      Purchase Address   Sales  Month  \\\n", "0 2019-04-19 08:46:00          917 1st St, Dallas, TX 75001   23.90      4   \n", "2 2019-04-07 22:30:00     682 Chestnut St, Boston, MA 02215   99.99      4   \n", "3 2019-04-12 14:38:00  669 Spruce St, Los Angeles, CA 90001  600.00      4   \n", "4 2019-04-12 14:38:00  669 Spruce St, Los Angeles, CA 90001   11.99      4   \n", "5 2019-04-30 09:27:00     333 8th St, Los Angeles, CA 90001   11.99      4   \n", "\n", "   Hour         City  \n", "0     8       Dallas  \n", "2    22       Boston  \n", "3    14  Los Angeles  \n", "4    14  Los Angeles  \n", "5     9  Los Angeles  "]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from itertools import combinations\n", "import glob, os\n", "\n", "# Load CSV files\n", "files = glob.glob('Sales_*.csv') or glob.glob('monthly_sale_2019/Sales_*.csv')\n", "df = pd.concat([pd.read_csv(f) for f in files], ignore_index=True)\n", "df.dropna(inplace=True)\n", "df = df[~df['Order Date'].str.contains('Order Date', na=False)]\n", "\n", "# Process data\n", "df['Quantity Ordered'] = pd.to_numeric(df['Quantity Ordered'])\n", "df['Price Each'] = pd.to_numeric(df['Price Each'])\n", "df['Order Date'] = pd.to_datetime(df['Order Date'])\n", "df['Sales'] = df['Quantity Ordered'] * df['Price Each']\n", "df['Month'] = df['Order Date'].dt.month\n", "df['Hour'] = df['Order Date'].dt.hour\n", "df['City'] = df['Purchase Address'].str.split(',').str[1].str.strip()\n", "\n", "print(f'Loaded {len(df):,} records, Total Sales: ${df[\"Sales\"].sum():,.0f}')\n", "df.head()"]}, {"cell_type": "markdown", "id": "q1", "metadata": {}, "source": ["## 1. Which month has the highest sales?"]}, {"cell_type": "code", "execution_count": 72, "id": "a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Peak: 12 ($4.6M), Low: 1 ($1.8M)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["monthly = df.groupby('Month')['Sales'].sum()\n", "print(f'Peak: {monthly.idxmax()} (${monthly.max()/1e6:.1f}M), Low: {monthly.idxmin()} (${monthly.min()/1e6:.1f}M)')\n", "ax = monthly.plot(kind='bar', figsize=(10,6), title='Monthly Sales', color='skyblue')\n", "plt.ylabel('Sales (Million USD)'); plt.xticks(rotation=0)\n", "ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e6:.1f}M'))\n", "for i, v in enumerate(monthly): ax.text(i, v + v*0.01, f'${v/1e6:.1f}M', ha='center', va='bottom', fontsize=9)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "q2", "metadata": {}, "source": ["## 2. Which city has the highest sales?"]}, {"cell_type": "code", "execution_count": 73, "id": "a2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["San Francisco: $8,262,204\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["city = df.groupby('City')['Sales'].sum()\n", "print(f'{city.idxmax()}: ${city.max():,.0f}')\n", "top_cities = city.nlargest(5)\n", "ax = top_cities.plot(kind='bar', figsize=(10,6), title='Top 5 Cities by Sales')\n", "plt.ylabel('Sales (Million USD)'); plt.xticks(rotation=45)\n", "ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'${x/1e6:.1f}M'))\n", "for i, v in enumerate(top_cities): ax.text(i, v + v*0.01, f'${v/1e6:.1f}M', ha='center', va='bottom', fontsize=9)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "q3", "metadata": {}, "source": ["## 3. What time has the most orders?"]}, {"cell_type": "code", "execution_count": 74, "id": "a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["19PM: 12,905 orders\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["hourly = df.groupby('Hour').size()\n", "print(f'{hourly.idxmax()}PM: {hourly.max():,} orders')\n", "ax = hourly.plot(kind='line', figsize=(10,6), title='Orders by Hour', marker='o', color='orange')\n", "plt.xlabel('Hour'); plt.ylabel('Number of Orders')\n", "for hour, count in hourly.items(): ax.annotate(f'{count:,}', (hour, count), textcoords='offset points', xytext=(0,10), ha='center', fontsize=8)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "q4", "metadata": {}, "source": ["## 4. Which product sells the most?"]}, {"cell_type": "code", "execution_count": 75, "id": "a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AAA Batteries (4-pack): 31,017 units\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["product = df.groupby('Product')['Quantity Ordered'].sum()\n", "print(f'{product.idxmax()}: {product.max():,} units')\n", "top_products = product.nlargest(5)\n", "ax = top_products.plot(kind='barh', figsize=(12,6), title='Top 5 Best Selling Products', color='lightgreen')\n", "plt.xlabel('Quantity Sold (units)'); ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:,.0f}'))\n", "for i, v in enumerate(top_products): ax.text(v + v*0.01, i, f'{v:,.0f}', va='center', fontsize=9)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "q5", "metadata": {}, "source": ["## 5. Which products are often sold together?"]}, {"cell_type": "code", "execution_count": 76, "id": "a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Lightning Charging Cable + iPhone: 1015 times\n", "Google Phone + USB-C Charging Cable: 999 times\n", "Wired Headphones + iPhone: 462 times\n"]}], "source": ["from collections import Counter\n", "pairs = [tuple(sorted(combo)) for products in df.groupby('Order ID')['Product'].apply(list) for combo in combinations(products, 2) if len(products) > 1]\n", "for pair, count in Counter(pairs).most_common(3): print(f'{pair[0]} + {pair[1]}: {count} times')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}