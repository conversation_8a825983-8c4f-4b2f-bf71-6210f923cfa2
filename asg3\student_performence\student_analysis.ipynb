{"cells": [{"cell_type": "markdown", "id": "19a05eb2", "metadata": {}, "source": ["Lab 2- Data Science Methodology\n"]}, {"cell_type": "code", "execution_count": 13, "id": "6804236c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n"]}, {"cell_type": "code", "execution_count": 14, "id": "36568f92", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gender</th>\n", "      <th>race/ethnicity</th>\n", "      <th>parental level of education</th>\n", "      <th>lunch</th>\n", "      <th>test preparation course</th>\n", "      <th>math score</th>\n", "      <th>reading score</th>\n", "      <th>writing score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>female</td>\n", "      <td>group B</td>\n", "      <td>bachelor's degree</td>\n", "      <td>standard</td>\n", "      <td>none</td>\n", "      <td>72</td>\n", "      <td>72</td>\n", "      <td>74</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>female</td>\n", "      <td>group C</td>\n", "      <td>some college</td>\n", "      <td>standard</td>\n", "      <td>completed</td>\n", "      <td>69</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>female</td>\n", "      <td>group B</td>\n", "      <td>master's degree</td>\n", "      <td>standard</td>\n", "      <td>none</td>\n", "      <td>90</td>\n", "      <td>95</td>\n", "      <td>93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>male</td>\n", "      <td>group A</td>\n", "      <td>associate's degree</td>\n", "      <td>free/reduced</td>\n", "      <td>none</td>\n", "      <td>47</td>\n", "      <td>57</td>\n", "      <td>44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>male</td>\n", "      <td>group C</td>\n", "      <td>some college</td>\n", "      <td>standard</td>\n", "      <td>none</td>\n", "      <td>76</td>\n", "      <td>78</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>995</th>\n", "      <td>female</td>\n", "      <td>group E</td>\n", "      <td>master's degree</td>\n", "      <td>standard</td>\n", "      <td>completed</td>\n", "      <td>88</td>\n", "      <td>99</td>\n", "      <td>95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>996</th>\n", "      <td>male</td>\n", "      <td>group C</td>\n", "      <td>high school</td>\n", "      <td>free/reduced</td>\n", "      <td>none</td>\n", "      <td>62</td>\n", "      <td>55</td>\n", "      <td>55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>997</th>\n", "      <td>female</td>\n", "      <td>group C</td>\n", "      <td>high school</td>\n", "      <td>free/reduced</td>\n", "      <td>completed</td>\n", "      <td>59</td>\n", "      <td>71</td>\n", "      <td>65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>998</th>\n", "      <td>female</td>\n", "      <td>group D</td>\n", "      <td>some college</td>\n", "      <td>standard</td>\n", "      <td>completed</td>\n", "      <td>68</td>\n", "      <td>78</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>999</th>\n", "      <td>female</td>\n", "      <td>group D</td>\n", "      <td>some college</td>\n", "      <td>free/reduced</td>\n", "      <td>none</td>\n", "      <td>77</td>\n", "      <td>86</td>\n", "      <td>86</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1000 rows × 8 columns</p>\n", "</div>"], "text/plain": ["     gender race/ethnicity parental level of education         lunch  \\\n", "0    female        group B           bachelor's degree      standard   \n", "1    female        group C                some college      standard   \n", "2    female        group B             master's degree      standard   \n", "3      male        group A          associate's degree  free/reduced   \n", "4      male        group C                some college      standard   \n", "..      ...            ...                         ...           ...   \n", "995  female        group E             master's degree      standard   \n", "996    male        group C                 high school  free/reduced   \n", "997  female        group C                 high school  free/reduced   \n", "998  female        group D                some college      standard   \n", "999  female        group D                some college  free/reduced   \n", "\n", "    test preparation course  math score  reading score  writing score  \n", "0                      none          72             72             74  \n", "1                 completed          69             90             88  \n", "2                      none          90             95             93  \n", "3                      none          47             57             44  \n", "4                      none          76             78             75  \n", "..                      ...         ...            ...            ...  \n", "995               completed          88             99             95  \n", "996                    none          62             55             55  \n", "997               completed          59             71             65  \n", "998               completed          68             78             77  \n", "999                    none          77             86             86  \n", "\n", "[1000 rows x 8 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(\"StudentsPerformance.csv\")\n", "df\n"]}, {"cell_type": "code", "execution_count": 3, "id": "9f3d70f8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1000 entries, 0 to 999\n", "Data columns (total 8 columns):\n", " #   Column                       Non-Null Count  Dtype \n", "---  ------                       --------------  ----- \n", " 0   gender                       1000 non-null   object\n", " 1   race/ethnicity               1000 non-null   object\n", " 2   parental level of education  1000 non-null   object\n", " 3   lunch                        1000 non-null   object\n", " 4   test preparation course      1000 non-null   object\n", " 5   math score                   1000 non-null   int64 \n", " 6   reading score                1000 non-null   int64 \n", " 7   writing score                1000 non-null   int64 \n", "dtypes: int64(3), object(5)\n", "memory usage: 62.6+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 4, "id": "5df55aa8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>math score</th>\n", "      <th>reading score</th>\n", "      <th>writing score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1000.00000</td>\n", "      <td>1000.000000</td>\n", "      <td>1000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>66.08900</td>\n", "      <td>69.169000</td>\n", "      <td>68.054000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>15.16308</td>\n", "      <td>14.600192</td>\n", "      <td>15.195657</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.00000</td>\n", "      <td>17.000000</td>\n", "      <td>10.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>57.00000</td>\n", "      <td>59.000000</td>\n", "      <td>57.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>66.00000</td>\n", "      <td>70.000000</td>\n", "      <td>69.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>77.00000</td>\n", "      <td>79.000000</td>\n", "      <td>79.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>100.00000</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       math score  reading score  writing score\n", "count  1000.00000    1000.000000    1000.000000\n", "mean     66.08900      69.169000      68.054000\n", "std      15.16308      14.600192      15.195657\n", "min       0.00000      17.000000      10.000000\n", "25%      57.00000      59.000000      57.750000\n", "50%      66.00000      70.000000      69.000000\n", "75%      77.00000      79.000000      79.000000\n", "max     100.00000     100.000000     100.000000"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df .describe()"]}, {"cell_type": "code", "execution_count": 5, "id": "0081518f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df['math score'], kde=True)\n", "plt.title(\"Distribution of Math Scores\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 6, "id": "c7f4d3dd", "metadata": {}, "outputs": [], "source": ["df['average_score'] = (df['math score'] + df['reading score'] + df['writing score']) / 3\n", "df['pass'] = df['average_score'].apply(lambda x: 1 if x >= 50 else 0)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "64c7c88d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Probability pass/fail:\n", "pass\n", "1    0.897\n", "0    0.103\n", "Name: proportion, dtype: float64\n"]}], "source": ["# T<PERSON><PERSON> x<PERSON>c su<PERSON> h<PERSON>c sinh pass (pass = 1) và fail (pass = 0)\n", "pass_prob = df['pass'].value_counts(normalize=True)\n", "print(\"Probability pass/fail:\")\n", "print(pass_prob)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "bc8bad4f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pass probability if the test preparation course was completed:\n", "pass\n", "1    0.949721\n", "0    0.050279\n", "Name: proportion, dtype: float64\n"]}], "source": ["# <PERSON><PERSON><PERSON><PERSON><PERSON> họ<PERSON> sinh pass nếu đã tham gia khóa học luyện thi\n", "prep_pass_prob = df[df['test preparation course'] == 'completed']['pass'].value_counts(normalize=True)\n", "print(\"Pass probability if the test preparation course was completed:\")\n", "print(prep_pass_prob)\n"]}, {"cell_type": "code", "execution_count": 12, "id": "b4fc9d7e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pass probability if the test preparation course was NOT completed:\n", "pass\n", "1    0.867601\n", "0    0.132399\n", "Name: proportion, dtype: float64\n"]}], "source": ["no_prep_pass_prob = df[df['test preparation course'] == 'none']['pass'].value_counts(normalize=True)\n", "print(\"Pass probability if the test preparation course was NOT completed:\")\n", "print(no_prep_pass_prob)\n"]}, {"cell_type": "code", "execution_count": 11, "id": "96fdfd2d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.barplot(x='test preparation course', y='pass', data=df)\n", "plt.title(\"Pass Rate by Test Preparation Course Status\")\n", "plt.ylabel(\"Pass Probability\")\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}