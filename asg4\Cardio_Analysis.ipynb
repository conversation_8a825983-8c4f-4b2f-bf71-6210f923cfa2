pip install pandas numpy matplotlib seaborn scikit-learn statsmodels ibm_db scipy

# Cell 1.1: Đọ<PERSON> dữ liệu và kiểm tra thông tin
import pandas as pd
df = pd.read_csv('cardio_train.csv', sep=';')
print("Thông tin dữ liệu:")
print(df.info())

# Cell 1.2: Thống kê mô tả
print("Thống kê mô tả:")
print(df.describe())

# Cell 1.3: Kiểm tra giá trị rỗng
print("Giá trị rỗng:")
print(df.isnull().sum())

# Cell 2.1: Ghi chú về tải dữ liệu và truy vấn SQL
"""
Hướng dẫn:
1. Đăng nhập IBM Cloud, truy cập IBM DB2.
2. Tạo bảng CARDIO_TRAIN:
   CREATE TABLE CARDIO_TRAIN (
       ID INT, AGE INT, GENDER INT, HEIGHT INT, WEIGHT FLOAT, AP_HI INT, AP_LO INT,
       CHOLESTEROL INT, GLUC INT, SMOKE INT, ALCO INT, ACTIVE INT, CARDIO INT
   );
3. Tải file cardio_train.csv vào bảng qua giao diện DB2.
4. Chạy các truy vấn SQL trên console:
   - Đếm số hàng: SELECT COUNT(*) FROM CARDIO_TRAIN;
   - Xem 10 hàng: SELECT * FROM CARDIO_TRAIN LIMIT 10;
   - Phân bố giới tính: SELECT GENDER, COUNT(*) FROM CARDIO_TRAIN GROUP BY GENDER;
   - Tuổi theo năm: SELECT ID, AGE/365 AS AGE_YEARS FROM CARDIO_TRAIN LIMIT 10;
   - AP_HI cao nhất: SELECT * FROM CARDIO_TRAIN ORDER BY AP_HI DESC LIMIT 10;
   - AP_LO thấp nhất: SELECT * FROM CARDIO_TRAIN ORDER BY AP_LO ASC LIMIT 10;
   - GLUC = 3: SELECT * FROM CARDIO_TRAIN WHERE GLUC = 3 LIMIT 10;
   - CHOLESTEROL = 3: SELECT * FROM CARDIO_TRAIN WHERE CHOLESTEROL = 3 LIMIT 10;
   - ALCO = 1: SELECT * FROM CARDIO_TRAIN WHERE ALCO = 1 LIMIT 10;
"""
print("Truy vấn SQL đã thực hiện trên DB2 Console. Kết quả lưu trong báo cáo.")

# Cell 3.1: Kết nối với IBM DB2
import ibm_db
import pandas as pd
dsn = (
    "DATABASE=your_db;HOSTNAME=your_host;PORT=your_port;"
    "PROTOCOL=TCPIP;UID=your_username;PWD=your_password;"
)
try:
    global conn
    conn = ibm_db.connect(dsn, "", "")
    print("Kết nối thành công với IBM DB2!")
except:
    print("Kết nối thất bại. Kiểm tra thông tin xác thực.")
def run_query(query):
    stmt = ibm_db.exec_immediate(conn, query)
    result = []
    columns = [ibm_db.field_name(stmt, i) for i in range(ibm_db.num_fields(stmt))]
    while ibm_db.fetch_row(stmt):
        row = [ibm_db.result(stmt, i) for i in range(ibm_db.num_fields(stmt))]
        result.append(row)
    return pd.DataFrame(result, columns=columns)

# Cell 3.2: Đếm số hàng
query = "SELECT COUNT(*) AS TOTAL_ROWS FROM CARDIO_TRAIN"
result = run_query(query)
print("Số hàng trong dữ liệu:\n", result)

# Cell 3.3: Xem 10 hàng đầu tiên
query = "SELECT * FROM CARDIO_TRAIN LIMIT 10"
result = run_query(query)
print("10 hàng đầu tiên:\n", result)

# Cell 3.4: Phân bố giới tính
query = "SELECT GENDER, COUNT(*) AS COUNT FROM CARDIO_TRAIN GROUP BY GENDER"
result = run_query(query)
print("Phân bố giới tính:\n", result)

# Cell 3.5: Tuổi theo năm
query = "SELECT ID, AGE/365 AS AGE_YEARS FROM CARDIO_TRAIN LIMIT 10"
result = run_query(query)
print("Tuổi (năm) của 10 bệnh nhân đầu tiên:\n", result)

# Cell 3.6: 10 bệnh nhân AP_HI cao nhất
query = "SELECT * FROM CARDIO_TRAIN ORDER BY AP_HI DESC LIMIT 10"
result = run_query(query)
print("10 bệnh nhân có AP_HI cao nhất:\n", result)

# Cell 3.7: 10 bệnh nhân AP_LO thấp nhất
query = "SELECT * FROM CARDIO_TRAIN ORDER BY AP_LO ASC LIMIT 10"
result = run_query(query)
print("10 bệnh nhân có AP_LO thấp nhất:\n", result)

# Cell 3.8: 10 bệnh nhân GLUC = 3
query = "SELECT * FROM CARDIO_TRAIN WHERE GLUC = 3 LIMIT 10"
result = run_query(query)
print("10 bệnh nhân có GLUC = 3:\n", result)

# Cell 3.9: 10 bệnh nhân CHOLESTEROL = 3
query = "SELECT * FROM CARDIO_TRAIN WHERE CHOLESTEROL = 3 LIMIT 10"
result = run_query(query)
print("10 bệnh nhân có CHOLESTEROL = 3:\n", result)

# Cell 3.10: 10 bệnh nhân ALCO = 1
query = "SELECT * FROM CARDIO_TRAIN WHERE ALCO = 1 LIMIT 10"
result = run_query(query)
print("10 bệnh nhân uống rượu:\n", result)

# Cell 3.11: Đóng kết nối
if db2_available and conn is not None:
    ibm_db.close(conn)
    print("Đã đóng kết nối DB2.")
else:
    print("Không có kết nối DB2 để đóng.")

# Cell 4.1: Xuất dữ liệu
import ibm_db_dbi
conn = ibm_db_dbi.connect(dsn, "", "")
df_db2 = pd.read_sql("SELECT * FROM CARDIO_TRAIN", conn)
df_db2.to_csv("cardio_train_exported.csv", index=False)
conn.close()
df = pd.read_csv("cardio_train_exported.csv")
print("Đã xuất dữ liệu thành cardio_train_exported.csv")

# Cell 5.1: Tính tương quan
corr = df.corr()['CARDIO'].sort_values(ascending=False)
print("Tương quan với CARDIO:\n", corr)

# Cell 5.2: Biểu đồ nhiệt
import seaborn as sns
import matplotlib.pyplot as plt
corr = df[['AGE', 'GENDER', 'HEIGHT', 'WEIGHT', 'AP_HI', 'AP_LO', 'CHOLESTEROL', 'GLUC', 'SMOKE', 'ALCO', 'ACTIVE', 'CARDIO']].corr()
plt.figure(figsize=(12, 7))
sns.heatmap(corr, cmap='RdYlGn', annot=True)
plt.title("Correlation Heatmap")
plt.savefig("heatmap.png")
plt.show()

# Cell 5.3: Biểu đồ histogram
df[['AGE', 'HEIGHT', 'WEIGHT', 'AP_HI', 'AP_LO', 'CHOLESTEROL', 'GLUC', 'SMOKE', 'ALCO', 'ACTIVE', 'CARDIO']].hist(figsize=(16, 12))
plt.savefig("histogram.png")
plt.show()

# Cell 5.4: Biểu đồ hồi quy
sns.regplot(x="AGE", y="CARDIO", data=df)
plt.title("Regression Plot of AGE vs CARDIO")
plt.savefig("regression_plot.png")
plt.show()

# Cell 5.5: Biểu đồ hộp AP_HI
plt.figure(figsize=(6, 4))
sns.boxplot(y="AP_HI", data=df)
plt.title("Box Plot of AP_HI")
plt.savefig("boxplot_ap_hi.png")
plt.show()

# Cell 5.6: Biểu đồ hộp AP_LO
plt.figure(figsize=(6, 4))
sns.boxplot(y="AP_LO", data=df)
plt.title("Box Plot of AP_LO")
plt.savefig("boxplot_ap_lo.png")
plt.show()

# Cell 6.1: Xử lý ngoại lai AP_HI
first_quantile, third_quantile = df['AP_HI'].quantile([0.25, 0.75])
iqr = third_quantile - first_quantile
maximum = third_quantile + 1.5 * iqr
minimum = first_quantile - 1.5 * iqr
df['AP_HI'] = df['AP_HI'].clip(lower=minimum, upper=maximum)
plt.figure(figsize=(6, 4))
sns.boxplot(y="AP_HI", data=df)
plt.title("Box Plot of AP_HI After Outlier Handling")
plt.savefig("boxplot_ap_hi_cleaned.png")
plt.show()

# Cell 6.2: Xử lý ngoại lai AP_LO
first_quantile, third_quantile = df['AP_LO'].quantile([0.25, 0.75])
iqr = third_quantile - first_quantile
maximum = third_quantile + 1.5 * iqr
minimum = first_quantile - 1.5 * iqr
df['AP_LO'] = df['AP_LO'].clip(lower=minimum, upper=maximum)
plt.figure(figsize=(6, 4))
sns.boxplot(y="AP_LO", data=df)
plt.title("Box Plot of AP_LO After Outlier Handling")
plt.savefig("boxplot_ap_lo_cleaned.png")
plt.show()

# Cell 6.3: Chuẩn hóa dữ liệu
from sklearn.preprocessing import StandardScaler
import numpy as np
scaler = StandardScaler()
data_scaled = scaler.fit_transform(df[['AGE', 'HEIGHT', 'WEIGHT', 'AP_HI', 'AP_LO']])
data = np.concatenate([data_scaled, df[['GENDER', 'CHOLESTEROL', 'GLUC', 'SMOKE', 'ALCO', 'ACTIVE', 'CARDIO']].values], axis=1)
global X, y
X = data[:, :-1]
y = data[:, -1]
print("Data standardized successfully")

# Cell 7.1: ANOVA cho Cholesterol
from scipy.stats import f_oneway
low = df[df['CHOLESTEROL'] == 1]['CARDIO']
high = df[df['CHOLESTEROL'] == 2]['CARDIO']
very_high = df[df['CHOLESTEROL'] == 3]['CARDIO']
f_stat, p_val = f_oneway(low, high, very_high)
print(f"ANOVA: F-statistic={f_stat}, p-value={p_val}")

# Cell 7.2: T-Test cho Age
from scipy.stats import ttest_ind
mean_age = df['AGE'].mean()
lower_age = df[df['AGE'] <= mean_age]['CARDIO']
higher_age = df[df['AGE'] > mean_age]['CARDIO']
t_stat, p_val = ttest_ind(lower_age, higher_age)
print(f"T-Test: T-statistic={t_stat}, p-value={p_val}")

# Cell 7.3: Chi-Square cho Alcohol
from scipy.stats import chi2_contingency
table = pd.crosstab(df['ALCO'], df['CARDIO'])
chi2, p_val, _, _ = chi2_contingency(table)
print(f"Chi-Square: Chi2={chi2}, p-value={p_val}")

# Cell 7.4: Mô hình OLS
import statsmodels.api as sm
X_ols = df[['AGE', 'GENDER', 'HEIGHT', 'WEIGHT', 'AP_HI', 'AP_LO', 'CHOLESTEROL', 'GLUC', 'SMOKE', 'ALCO', 'ACTIVE']]
X_ols = sm.add_constant(X_ols)
y_ols = df['CARDIO']
model = sm.OLS(y_ols, X_ols).fit()
print(model.summary())

# Cell 8.1: Chia dữ liệu
from sklearn.model_selection import train_test_split
global X_train, X_test, y_train, y_test
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
print(f"Training set size: {X_train.shape[0]}")
print(f"Test set size: {X_test.shape[0]}")

# Cell 8.2: Ridge Classifier
from sklearn.linear_model import RidgeClassifier
from sklearn.metrics import accuracy_score
ridge = RidgeClassifier(alpha=0.0001)
ridge.fit(X_train, y_train)
print("Ridge Accuracy:", accuracy_score(y_test, ridge.predict(X_test)))

# Cell 8.3: Random Forest
from sklearn.ensemble import RandomForestClassifier
rf = RandomForestClassifier()
rf.fit(X_train, y_train)
print("Random Forest Accuracy:", accuracy_score(y_test, rf.predict(X_test)))

# Cell 8.4: Gradient Boosting
from sklearn.ensemble import GradientBoostingClassifier
gbc = GradientBoostingClassifier()
gbc.fit(X_train, y_train)
print("Gradient Boosting Accuracy:", accuracy_score(y_test, gbc.predict(X_test)))

# Cell 8.5: AdaBoost
from sklearn.ensemble import AdaBoostClassifier
ada = AdaBoostClassifier(n_estimators=100, random_state=0)
ada.fit(X_train, y_train)
print("AdaBoost Accuracy:", accuracy_score(y_test, ada.predict(X_test)))

# Cell 8.6: Bagging
from sklearn.ensemble import BaggingClassifier
from sklearn.neighbors import KNeighborsClassifier
bagging = BaggingClassifier(KNeighborsClassifier(), max_samples=0.5, max_features=0.5)
bagging.fit(X_train, y_train)
print("Bagging Accuracy:", accuracy_score(y_test, bagging.predict(X_test)))

# Cell 8.7: Extra Trees
from sklearn.ensemble import ExtraTreesClassifier
extra = ExtraTreesClassifier(n_estimators=100, random_state=0)
extra.fit(X_train, y_train)
print("Extra Trees Accuracy:", accuracy_score(y_test, extra.predict(X_test)))

# Cell 8.8: Stacking
from sklearn.ensemble import StackingClassifier
from sklearn.neighbors import KNeighborsClassifier
estimators = [('ridge', RidgeClassifier()), ('knr', KNeighborsClassifier(n_neighbors=20, metric='euclidean'))]
final = GradientBoostingClassifier(n_estimators=25, subsample=0.5, min_samples_leaf=25, max_features=1, random_state=42)
stack = StackingClassifier(estimators=estimators, final_estimator=final)
stack.fit(X_train, y_train)
print("Stacking Accuracy:", accuracy_score(y_test, stack.predict(X_test)))

# Cell 9.1: Watson Studio Setup
"""
Hướng dẫn chạy trên Watson Studio:

1. Đăng nhập IBM Cloud (https://cloud.ibm.com)
2. Tạo Watson Studio service:
   - Tìm "Watson Studio" trong catalog
   - Chọn plan "Lite" (miễn phí)
   - Tạo service

3. Tạo project mới:
   - Click "Create a project"
   - Chọn "Create an empty project"
   - Đặt tên: "Cardiovascular Disease Analysis"
   - Tạo Cloud Object Storage nếu chưa có

4. Upload dữ liệu:
   - Trong project, click "Add to project" > "Data"
   - Upload file cardio_train.csv

5. Tạo notebook:
   - Click "Add to project" > "Notebook"
   - Chọn "From file" và upload file .ipynb này
   - Hoặc "From URL" nếu đã có trên GitHub

6. Chạy notebook:
   - Chọn Python 3.x environment
   - Run từng cell hoặc "Run All"
"""
print("Watson Studio setup instructions displayed above")

# Cell 9.2: GitHub Repository Setup
"""
Hướng dẫn đẩy lên GitHub:

1. Tạo GitHub repository:
   - Đăng nhập GitHub (https://github.com)
   - Click "New repository"
   - Tên: "cardiovascular-disease-analysis"
   - Description: "Machine Learning Analysis of Cardiovascular Disease Dataset"
   - Chọn "Public" và "Add README"

2. Clone repository về máy:
   git clone https://github.com/username/cardiovascular-disease-analysis.git
   cd cardiovascular-disease-analysis

3. Copy files vào repository:
   - Cardio_Analysis.ipynb
   - cardio_train.csv
   - Các file hình ảnh (.png)

4. Commit và push:
   git add .
   git commit -m "Add cardiovascular disease analysis notebook"
   git push origin main

5. Tạo README.md với nội dung:
   - Mô tả project
   - Hướng dẫn chạy
   - Kết quả chính
   - Requirements
"""
print("GitHub setup instructions displayed above")

# Cell 9.3: Tóm tắt kết quả project
print("=== CARDIOVASCULAR DISEASE ANALYSIS SUMMARY ===")
print("\n1. Data Overview:")
print(f"   - Total samples: {len(df)}")
print(f"   - Features: {len(df.columns)-1}")
print(f"   - Target: CARDIO (0: No disease, 1: Disease)")

print("\n2. Data Processing:")
print("   - Outlier handling using IQR method")
print("   - Standardization of numerical features")
print("   - No missing values found")

print("\n3. Statistical Analysis:")
print("   - ANOVA test for Cholesterol vs CARDIO")
print("   - T-test for Age groups vs CARDIO")
print("   - Chi-square test for Alcohol vs CARDIO")
print("   - OLS regression model")

print("\n4. Machine Learning Models Tested:")
models = ['Ridge Classifier', 'Random Forest', 'Gradient Boosting', 
          'AdaBoost', 'Bagging', 'Extra Trees', 'Stacking']
for model in models:
    print(f"   - {model}")

print("\n5. Visualizations Created:")
plots = ['Correlation Heatmap', 'Histograms', 'Regression Plot', 
         'Box Plots (before/after outlier handling)']
for plot in plots:
    print(f"   - {plot}")

print("\n6. Files Generated:")
files = ['heatmap.png', 'histogram.png', 'regression_plot.png',
         'boxplot_ap_hi.png', 'boxplot_ap_lo.png', 
         'boxplot_ap_hi_cleaned.png', 'boxplot_ap_lo_cleaned.png']
for file in files:
    print(f"   - {file}")

print("\n=== PROJECT COMPLETED SUCCESSFULLY ===")

# Cell 9.4: Tạo file requirements.txt
requirements = """
pandas>=1.3.0
numpy>=1.21.0
matplotlib>=3.4.0
seaborn>=0.11.0
scikit-learn>=1.0.0
statsmodels>=0.12.0
scipy>=1.7.0
ibm_db>=3.1.0
jupyter>=1.0.0
"""

with open('requirements.txt', 'w') as f:
    f.write(requirements.strip())
    
print("Requirements.txt file created successfully!")
print("\nTo install dependencies, run:")
print("pip install -r requirements.txt")