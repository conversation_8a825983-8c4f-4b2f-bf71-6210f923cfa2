pip install pandas numpy matplotlib seaborn scikit-learn statsmodels ibm_db scipy

# Cell 1.1: Đọ<PERSON> dữ liệu và kiểm tra thông tin
import pandas as pd
df = pd.read_csv('cardio_train.csv', sep=';')
print("Thông tin dữ liệu:")
print(df.info())

# Cell 1.2: Thống kê mô tả
print("Thống kê mô tả:")
print(df.describe())

# Cell 1.3: Kiểm tra giá trị rỗng
print("Giá trị rỗng:")
print(df.isnull().sum())

# Cell 2.1: Ghi chú về tải dữ liệu và truy vấn SQL
"""
Hướng dẫn:
1. Đăng nhập IBM Cloud, truy cập IBM DB2.
2. Tạo bảng CARDIO_TRAIN:
   CREATE TABLE CARDIO_TRAIN (
       ID INT, AGE INT, GENDER INT, HEIGHT INT, WEIGHT FLOAT, AP_HI INT, AP_LO INT,
       CHOLESTEROL INT, GLUC INT, SMOKE INT, ALCO INT, ACTIVE INT, CARDIO INT
   );
3. Tải file cardio_train.csv vào bảng qua giao diện DB2.
4. Chạy các truy vấn SQL trên console:
   - Đếm số hàng: SELECT COUNT(*) FROM CARDIO_TRAIN;
   - Xem 10 hàng: SELECT * FROM CARDIO_TRAIN LIMIT 10;
   - Phân bố giới tính: SELECT GENDER, COUNT(*) FROM CARDIO_TRAIN GROUP BY GENDER;
   - Tuổi theo năm: SELECT ID, AGE/365 AS AGE_YEARS FROM CARDIO_TRAIN LIMIT 10;
   - AP_HI cao nhất: SELECT * FROM CARDIO_TRAIN ORDER BY AP_HI DESC LIMIT 10;
   - AP_LO thấp nhất: SELECT * FROM CARDIO_TRAIN ORDER BY AP_LO ASC LIMIT 10;
   - GLUC = 3: SELECT * FROM CARDIO_TRAIN WHERE GLUC = 3 LIMIT 10;
   - CHOLESTEROL = 3: SELECT * FROM CARDIO_TRAIN WHERE CHOLESTEROL = 3 LIMIT 10;
   - ALCO = 1: SELECT * FROM CARDIO_TRAIN WHERE ALCO = 1 LIMIT 10;
"""
print("Truy vấn SQL đã thực hiện trên DB2 Console. Kết quả lưu trong báo cáo.")

# Cell 3.1: Kết nối với IBM DB2
import ibm_db
import pandas as pd
dsn = (
    "DATABASE=your_db;HOSTNAME=your_host;PORT=your_port;"
    "PROTOCOL=TCPIP;UID=your_username;PWD=your_password;"
)
try:
    global conn
    conn = ibm_db.connect(dsn, "", "")
    print("Kết nối thành công với IBM DB2!")
except:
    print("Kết nối thất bại. Kiểm tra thông tin xác thực.")
def run_query(query):
    stmt = ibm_db.exec_immediate(conn, query)
    result = []
    columns = [ibm_db.field_name(stmt, i) for i in range(ibm_db.num_fields(stmt))]
    while ibm_db.fetch_row(stmt):
        row = [ibm_db.result(stmt, i) for i in range(ibm_db.num_fields(stmt))]
        result.append(row)
    return pd.DataFrame(result, columns=columns)

# Cell 3.2: Đếm số hàng
query = "SELECT COUNT(*) AS TOTAL_ROWS FROM CARDIO_TRAIN"
result = run_query(query)
print("Số hàng trong dữ liệu:\n", result)

# Cell 3.3: Xem 10 hàng đầu tiên
query = "SELECT * FROM CARDIO_TRAIN LIMIT 10"
result = run_query(query)
print("10 hàng đầu tiên:\n", result)

# Cell 3.4: Phân bố giới tính
query = "SELECT GENDER, COUNT(*) AS COUNT FROM CARDIO_TRAIN GROUP BY GENDER"
result = run_query(query)
print("Phân bố giới tính:\n", result)

# Cell 3.5: Tuổi theo năm
query = "SELECT ID, AGE/365 AS AGE_YEARS FROM CARDIO_TRAIN LIMIT 10"
result = run_query(query)
print("Tuổi (năm) của 10 bệnh nhân đầu tiên:\n", result)

# Cell 3.6: 10 bệnh nhân AP_HI cao nhất
query = "SELECT * FROM CARDIO_TRAIN ORDER BY AP_HI DESC LIMIT 10"
result = run_query(query)
print("10 bệnh nhân có AP_HI cao nhất:\n", result)

# Cell 3.7: 10 bệnh nhân AP_LO thấp nhất
query = "SELECT * FROM CARDIO_TRAIN ORDER BY AP_LO ASC LIMIT 10"
result = run_query(query)
print("10 bệnh nhân có AP_LO thấp nhất:\n", result)

# Cell 3.8: 10 bệnh nhân GLUC = 3
query = "SELECT * FROM CARDIO_TRAIN WHERE GLUC = 3 LIMIT 10"
result = run_query(query)
print("10 bệnh nhân có GLUC = 3:\n", result)

# Cell 3.9: 10 bệnh nhân CHOLESTEROL = 3
query = "SELECT * FROM CARDIO_TRAIN WHERE CHOLESTEROL = 3 LIMIT 10"
result = run_query(query)
print("10 bệnh nhân có CHOLESTEROL = 3:\n", result)

# Cell 3.10: 10 bệnh nhân ALCO = 1
query = "SELECT * FROM CARDIO_TRAIN WHERE ALCO = 1 LIMIT 10"
result = run_query(query)
print("10 bệnh nhân uống rượu:\n", result)

# Bước 3.11: Đóng kết nối
ibm_db.close(conn)
print("Đã đóng kết nối.")

# Bước 4.1: Xuất dữ liệu
import ibm_db_dbi
conn = ibm_db_dbi.connect(dsn, "", "")
df_db2 = pd.read_sql("SELECT * FROM CARDIO_TRAIN", conn)
df_db2.to_csv("cardio_train_exported.csv", index=False)
conn.close()
df = pd.read_csv("cardio_train_exported.csv")
print("Đã xuất dữ liệu thành cardio_train_exported.csv")

# Bước 5.1: Tính tương quan
corr = df.corr()['CARDIO'].sort_values(ascending=False)
print("Tương quan với CARDIO:\n", corr)

# Bước 5.2: Biểu đồ nhiệt
import seaborn as sns
import matplotlib.pyplot as plt
corr = df[['AGE', 'GENDER', 'HEIGHT', 'WEIGHT', 'AP_HI', 'AP_LO', 'CHOLESTEROL', 'GLUC', 'SMOKE', 'ALCO', 'ACTIVE', 'CARDIO']].corr()
plt.figure(figsize=(12, 7))
sns.heatmap(corr, cmap='RdYlGn', annot=True)
plt.title("Correlation Heatmap")
plt.savefig("heatmap.png")
plt.show()

# Bước 5.3: Biểu đồ histogram
df[['AGE', 'HEIGHT', 'WEIGHT', 'AP_HI', 'AP_LO', 'CHOLESTEROL', 'GLUC', 'SMOKE', 'ALCO', 'ACTIVE', 'CARDIO']].hist(figsize=(16, 12))
plt.savefig("histogram.png")
plt.show()